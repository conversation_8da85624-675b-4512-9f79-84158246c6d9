package com.zrcoding.barbcker.data.repositories_impl

import androidx.datastore.core.DataStore
import com.tweener.passage.Passage
import com.tweener.passage.model.Entrant
import com.zrcoding.barbcker.data.database.AppDatabase
import com.zrcoding.barbcker.data.datastore.prefs.PreferencesDataSource
import com.zrcoding.barbcker.data.datastore.proto.toAccount
import com.zrcoding.barbcker.data.datastore.proto.toPbAccount
import com.zrcoding.barbcker.data.dtos.Constants
import com.zrcoding.barbcker.data.dtos.ShopInfoDto
import com.zrcoding.barbcker.domain.models.Account
import com.zrcoding.barbcker.domain.models.AuthErrors
import com.zrcoding.barbcker.domain.models.Currency
import com.zrcoding.barbcker.domain.models.NetworkErrors
import com.zrcoding.barbcker.domain.models.Resource
import com.zrcoding.barbcker.domain.models.failure
import com.zrcoding.barbcker.domain.models.success
import com.zrcoding.barbcker.domain.repositories.AccountRepository
import dev.gitlive.firebase.FirebaseNetworkException
import dev.gitlive.firebase.auth.FirebaseAuth
import dev.gitlive.firebase.firestore.FirebaseFirestore
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.IO
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.withContext
import se.scmv.morocco.datastore.PbAccount
import se.scmv.morocco.datastore.PbNotConnectedAccount

class AccountRepositoryImpl(
    private val accountDataStore: DataStore<PbAccount>,
    private val preferencesDataSource: PreferencesDataSource,
    private val passage: Passage,
    private val firebaseAuth: FirebaseAuth,
    private val firebaseFirestore: FirebaseFirestore,
    private val appDatabase: AppDatabase,
) : AccountRepository {

    companion object {
        const val LANGUAGE_KEY = "language"
    }

    override suspend fun saveAccount(entrant: Entrant, shopName: String?, currency: Currency?) {
        accountDataStore.updateData {
            entrant.toPbAccount(shopName.orEmpty(), currency ?: Currency.DOLLAR)
        }
    }

    override suspend fun updateAccount(
        shopName: String,
        currency: Currency
    ): Resource<Unit, AuthErrors> {
        return try {
            val user = firebaseAuth.currentUser
                ?: return Resource.Failure(AuthErrors.ShouldRelogIn)
            val info = ShopInfoDto(shopName, currency)
            val dto = hashMapOf(Constants.SHOP_INFO to info)
            firebaseFirestore.collection(Constants.USERS_COLLECTION)
                .document(documentPath = user.uid)
                .set(data = dto, merge = true)
            accountDataStore.updateData {
                it.copy(
                    notConnected = null,
                    connected = it.connected?.copy(shopName = shopName, currency = currency.name)
                )
            }
            Resource.Success(Unit)
        } catch (e: FirebaseNetworkException) {
            e.printStackTrace()
            Resource.Failure(AuthErrors.Network(NetworkErrors.NO_INTERNET))
        } catch (e: Exception) {
            e.printStackTrace()
            Resource.Failure(AuthErrors.Network(NetworkErrors.UNKNOWN))
        }
    }

    override fun getAccount(): Flow<Account> {
        return accountDataStore.data.map { it.toAccount() }
    }

    override suspend fun logout(): Resource<Unit, AuthErrors> {
        return try {
            passage.signOut()
            firebaseAuth.signOut()
            accountDataStore.updateData {
                PbAccount(notConnected = PbNotConnectedAccount(), connected = null)
            }
            success(Unit)
        } catch (e: FirebaseNetworkException) {
            e.printStackTrace()
            failure(AuthErrors.Network(NetworkErrors.NO_INTERNET))
        } catch (e: Exception) {
            e.printStackTrace()
            failure(AuthErrors.Network(NetworkErrors.UNKNOWN))
        }
    }

    override suspend fun deleteAccount(): Resource<Unit, AuthErrors> {
        return try {
            val currentUser = firebaseAuth.currentUser ?: return failure(AuthErrors.ShouldRelogIn)
            firebaseFirestore.collection(Constants.USERS_COLLECTION)
                .document(currentUser.uid)
                .delete()
            currentUser.delete()
            firebaseAuth.signOut()
            accountDataStore.updateData {
                PbAccount(notConnected = PbNotConnectedAccount(), connected = null)
            }
            withContext(Dispatchers.IO) {
                appDatabase.clearAllTables()
            }
            success(Unit)
            // TODO Catch GetCredentialCancellationException
        } catch (e: FirebaseNetworkException) {
            e.printStackTrace()
            failure(AuthErrors.Network(NetworkErrors.NO_INTERNET))
        } catch (e: Exception) {
            e.printStackTrace()
            failure(AuthErrors.Network(NetworkErrors.UNKNOWN))
        }
    }

    override suspend fun isLanguageSelected(): Boolean {
        return preferencesDataSource.getBoolean(key = LANGUAGE_KEY, defaultValue = false)
    }

    override suspend fun setSelectedLanguage() {
        preferencesDataSource.saveBoolean(key = LANGUAGE_KEY, value = true)
    }
}