package com.zrcoding.barbcker.data.repositories_impl

import com.tweener.passage.Passage
import com.tweener.passage.error.PassageEmailAddressAlreadyExistsException
import com.tweener.passage.gatekeeper.apple.error.PassageAppleGatekeeperException
import com.tweener.passage.gatekeeper.email.model.PassageEmailAuthParams
import com.tweener.passage.gatekeeper.google.error.PassageGoogleGatekeeperException
import com.tweener.passage.model.Entrant
import com.zrcoding.barbcker.data.database.AppDatabase
import com.zrcoding.barbcker.data.dtos.AccountDto
import com.zrcoding.barbcker.data.dtos.Constants
import com.zrcoding.barbcker.data.mappers.toBarberEntity
import com.zrcoding.barbcker.domain.models.AuthErrors
import com.zrcoding.barbcker.domain.models.AuthStatus
import com.zrcoding.barbcker.domain.models.NetworkErrors
import com.zrcoding.barbcker.domain.models.Resource
import com.zrcoding.barbcker.domain.models.SignUpErrors
import com.zrcoding.barbcker.domain.models.failure
import com.zrcoding.barbcker.domain.repositories.AccountRepository
import com.zrcoding.barbcker.domain.repositories.AuthRepository
import dev.gitlive.firebase.FirebaseNetworkException
import dev.gitlive.firebase.firestore.FirebaseFirestore

class AuthRepositoryImpl(
    private val passage: Passage,
    private val firebaseFirestore: FirebaseFirestore,
    private val accountRepository: AccountRepository,
    private val appDatabase: AppDatabase,
) : AuthRepository {

    override suspend fun authenticateWithEmailAndPassword(
        email: String,
        password: String
    ): Resource<AuthStatus, AuthErrors> {
        val emailPasswordParams = PassageEmailAuthParams(email = email, password = password)
        return authenticate { passage.authenticateWithEmailAndPassword(emailPasswordParams) }
    }

    override suspend fun authenticateWithGoogle(): Resource<AuthStatus, AuthErrors> {
        return authenticate { passage.authenticateWithGoogle() }
    }

    override suspend fun authenticateWithApple(): Resource<AuthStatus, AuthErrors> {
        return authenticate { passage.authenticateWithApple() }
    }

    private suspend fun authenticate(
        authMethod: suspend () -> Result<Entrant>
    ): Resource<AuthStatus, AuthErrors> {
        return try {
            val entrant = authMethod().getOrThrow()
            val accountDto = firebaseFirestore
                .collection(Constants.USERS_COLLECTION)
                .document(entrant.uid)
                .get()
            val completionStatus = if (accountDto.exists) {
                val (shopInfo, barbers) = accountDto.data(strategy = AccountDto.serializer())
                if (shopInfo == null) {
                    accountRepository.saveAccount(entrant = entrant)
                    AuthStatus.Connected.CompletionStatus.SHOULD_COMPLETE_ACCOUNT
                } else {
                    accountRepository.saveAccount(
                        entrant = entrant,
                        shopName = shopInfo.shopName,
                        currency = shopInfo.currency
                    )
                    if (barbers.isEmpty()) {
                        AuthStatus.Connected.CompletionStatus.SHOULD_SETUP_BARBERS
                    } else {
                        appDatabase.barberDao().insertMany(
                            barbers = barbers.map { it.toBarberEntity() }
                        )
                        AuthStatus.Connected.CompletionStatus.COMPLETED
                    }
                }
            } else {
                accountRepository.saveAccount(entrant = entrant)
                AuthStatus.Connected.CompletionStatus.SHOULD_COMPLETE_ACCOUNT
            }
            Resource.Success(
                AuthStatus.Connected(
                    uid = entrant.uid,
                    name = entrant.displayName.orEmpty(),
                    email = entrant.email.orEmpty(),
                    phoneNumber = entrant.phoneNumber.orEmpty(),
                    completionStatus = completionStatus
                )
            )
        } catch (e: PassageGoogleGatekeeperException) {
            e.printStackTrace()
            Resource.Success(AuthStatus.Canceled)
        } catch (e: PassageAppleGatekeeperException) {
            e.printStackTrace()
            Resource.Success(AuthStatus.Canceled)
        } catch (e: FirebaseNetworkException) {
            e.printStackTrace()
            Resource.Failure(AuthErrors.Network(NetworkErrors.NO_INTERNET))
        } catch (e: Exception) {
            e.printStackTrace()
            Resource.Failure(AuthErrors.Network(NetworkErrors.UNKNOWN))
        }
    }

    override suspend fun signUpWithEmailAndPassword(
        email: String,
        password: String
    ): Resource<Unit, SignUpErrors> {
        val emailPasswordParams = PassageEmailAuthParams(email = email, password = password)
        return passage.createUserWithEmailAndPassword(emailPasswordParams).fold(
            onSuccess = { entrant ->
                accountRepository.saveAccount(entrant = entrant)
                Resource.Success(Unit)
            },
            onFailure = {
                return when (it) {
                    is PassageEmailAddressAlreadyExistsException -> failure(SignUpErrors.EmailAlreadyExists)
                    else -> failure(SignUpErrors.Network(NetworkErrors.UNKNOWN))
                }
            }
        )
    }
}