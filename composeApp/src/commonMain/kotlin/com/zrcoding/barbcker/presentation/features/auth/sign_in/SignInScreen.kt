package com.zrcoding.barbcker.presentation.features.auth.sign_in

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.FilledIconButton
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButtonDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedIconButton
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.text.LinkAnnotation
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.TextLinkStyles
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.text.withLink
import androidx.compose.ui.unit.dp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import barbcker.composeapp.generated.resources.Res
import barbcker.composeapp.generated.resources.common_continue
import barbcker.composeapp.generated.resources.common_or
import barbcker.composeapp.generated.resources.ic_apple
import barbcker.composeapp.generated.resources.ic_google
import barbcker.composeapp.generated.resources.img_barber_chair
import barbcker.composeapp.generated.resources.sign_in_create_account
import barbcker.composeapp.generated.resources.sign_in_description
import barbcker.composeapp.generated.resources.sign_in_email_label
import barbcker.composeapp.generated.resources.sign_in_email_placeholder
import barbcker.composeapp.generated.resources.sign_in_password_label
import barbcker.composeapp.generated.resources.sign_in_password_placeholder
import barbcker.composeapp.generated.resources.sign_in_privacy_policy_part1
import barbcker.composeapp.generated.resources.sign_in_privacy_policy_part2
import barbcker.composeapp.generated.resources.sign_in_title
import com.zrcoding.barbcker.analytics.TrackScreenViewEvent
import com.zrcoding.barbcker.analytics.models.AnalyticsEvent
import com.zrcoding.barbcker.presentation.design_system.components.BcPrimaryButton
import com.zrcoding.barbcker.presentation.design_system.components.BcScreenDescription
import com.zrcoding.barbcker.presentation.design_system.components.BcScreenTitle
import com.zrcoding.barbcker.presentation.design_system.components.BcSecondaryButton
import com.zrcoding.barbcker.presentation.design_system.components.BcTextField
import com.zrcoding.barbcker.presentation.design_system.theme.BarbckerTheme
import com.zrcoding.barbcker.presentation.design_system.theme.dimension
import kotlinx.coroutines.flow.collectLatest
import org.jetbrains.compose.resources.painterResource
import org.jetbrains.compose.resources.stringResource
import org.jetbrains.compose.ui.tooling.preview.Preview
import org.koin.compose.viewmodel.koinViewModel

@Composable
fun SignInRoute(
    navigateToSignUp: () -> Unit,
    navigateToCompleteAccount: () -> Unit,
    navigateToSetupBarbers: () -> Unit,
    navigateToHome: () -> Unit,
    navigateToPrivacyPolicy: () -> Unit,
    viewModel: SignInViewModel = koinViewModel()
) {
    val viewState = viewModel.viewState.collectAsStateWithLifecycle().value
    SignInScreen(
        viewState = viewState,
        onEmailChanged = viewModel::onEmailChanged,
        onPasswordChanged = viewModel::onPasswordChanged,
        onSubmitClicked = viewModel::onSubmit,
        onCreateAccountClicked = navigateToSignUp,
        onGoogleBtnClicked = viewModel::onGoogleBtnClicked,
        onAppleBtnClicked = viewModel::onAppleBtnClicked,
        onPrivacyPolicyClicked = navigateToPrivacyPolicy,
        modifier = Modifier.fillMaxSize()
    )
    if (viewState.isLoading) {
        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(MaterialTheme.colorScheme.background.copy(alpha = 0.5f)),
            contentAlignment = Alignment.Center
        ) {
            CircularProgressIndicator()
        }
    }
    LaunchedEffect(Unit) {
        viewModel.oneTimeEvents.collectLatest { event ->
            when (event) {
                SignInOneTimeEvents.NavigateToHome -> navigateToHome()
                SignInOneTimeEvents.NavigateToCompleteAccount -> navigateToCompleteAccount()
                SignInOneTimeEvents.NavigateToSetupBarbers -> navigateToSetupBarbers()
            }
        }
    }
    TrackScreenViewEvent(screenName = AnalyticsEvent.ScreensNames.AUTH)
}

@Composable
private fun SignInScreen(
    viewState: SignInViewState,
    onEmailChanged: (String) -> Unit,
    onPasswordChanged: (String) -> Unit,
    onSubmitClicked: () -> Unit,
    onCreateAccountClicked: () -> Unit,
    onGoogleBtnClicked: () -> Unit,
    onAppleBtnClicked: () -> Unit,
    onPrivacyPolicyClicked: () -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier
            .fillMaxSize()
            .padding(
                horizontal = MaterialTheme.dimension.screenPaddingHorizontal,
                vertical = MaterialTheme.dimension.big,
            ),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.SpaceBetween
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .weight(1f),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimension.extraBig)
        ) {
            Image(
                modifier = Modifier.width(200.dp),
                painter = painterResource(Res.drawable.img_barber_chair),
                contentDescription = null,
                contentScale = ContentScale.Crop
            )
            Column(
                verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimension.small)
            ) {
                BcScreenTitle(
                    modifier = Modifier.fillMaxWidth(),
                    text = stringResource(Res.string.sign_in_title),
                    textAlign = TextAlign.Center
                )
                BcScreenDescription(
                    modifier = Modifier.fillMaxWidth(),
                    text = stringResource(Res.string.sign_in_description),
                    textAlign = TextAlign.Center
                )
            }
            Column(
                modifier = Modifier.fillMaxWidth(),
                verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimension.medium),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                val focusManager = LocalFocusManager.current
                BcTextField(
                    modifier = Modifier.fillMaxWidth(),
                    value = viewState.email,
                    onValueChanged = onEmailChanged,
                    title = Res.string.sign_in_email_label,
                    placeholder = Res.string.sign_in_email_placeholder,
                    error = viewState.emailError?.let { stringResource(it) },
                    keyboardOptions = KeyboardOptions(
                        keyboardType = KeyboardType.Email,
                        imeAction = ImeAction.Next,
                    ),
                    singleLine = true
                )
                BcTextField(
                    modifier = Modifier.fillMaxWidth(),
                    value = viewState.password,
                    onValueChanged = onPasswordChanged,
                    title = Res.string.sign_in_password_label,
                    placeholder = Res.string.sign_in_password_placeholder,
                    error = viewState.passwordError?.let { stringResource(it) },
                    keyboardOptions = KeyboardOptions(
                        keyboardType = KeyboardType.Password,
                        imeAction = ImeAction.Done,
                    ),
                    keyboardActions = KeyboardActions(
                        onDone = {
                            focusManager.clearFocus()
                            onSubmitClicked()
                        }
                    ),
                    singleLine = true
                )
                BcPrimaryButton(
                    modifier = Modifier.width(250.dp),
                    text = stringResource(Res.string.common_continue),
                    onClick = {
                        focusManager.clearFocus()
                        onSubmitClicked()
                    }
                )
                BcSecondaryButton(
                    modifier = Modifier.width(250.dp),
                    text = stringResource(Res.string.sign_in_create_account),
                    onClick = {
                        focusManager.clearFocus()
                        onCreateAccountClicked()
                    }
                )
            }
            Column(
                modifier = Modifier.fillMaxWidth(),
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimension.small)
            ) {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(MaterialTheme.dimension.small),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    HorizontalDivider(
                        modifier = Modifier.weight(1f),
                        thickness = 1.5.dp,
                    )
                    Text(
                        text = stringResource(Res.string.common_or),
                        style = MaterialTheme.typography.bodySmall,
                    )
                    HorizontalDivider(
                        modifier = Modifier.weight(1f),
                        thickness = 1.5.dp,
                    )
                }
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.Center,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    if (viewState.services.contains(AuthService.GOOGLE)) {
                        OutlinedIconButton(onClick = onGoogleBtnClicked) {
                            Image(
                                modifier = Modifier.size(MaterialTheme.dimension.bigger),
                                painter = painterResource(Res.drawable.ic_google),
                                contentDescription = null
                            )
                        }
                    }
                    if (viewState.services.contains(AuthService.APPLE)) {
                        FilledIconButton(
                            colors = IconButtonDefaults.filledIconButtonColors(
                                containerColor = MaterialTheme.colorScheme.onBackground,
                                contentColor = MaterialTheme.colorScheme.background,
                            ),
                            onClick = onAppleBtnClicked
                        ) {
                            Icon(
                                modifier = Modifier.size(MaterialTheme.dimension.bigger),
                                painter = painterResource(Res.drawable.ic_apple),
                                contentDescription = null
                            )
                        }
                    }
                }
            }
        }
        Text(
            text = buildAnnotatedString {
                append(stringResource(Res.string.sign_in_privacy_policy_part1))
                withLink(
                    link = LinkAnnotation.Clickable(
                        tag = "privacy_policy",
                        styles = TextLinkStyles(
                            style = SpanStyle(
                                fontWeight = MaterialTheme.typography.bodySmall.fontWeight,
                                fontSize = MaterialTheme.typography.bodySmall.fontSize,
                                textDecoration = TextDecoration.Underline
                            )
                        ),
                        linkInteractionListener = {
                            onPrivacyPolicyClicked()
                        }
                    )
                ) {
                    append(stringResource(Res.string.sign_in_privacy_policy_part2))
                }
            },
            style = MaterialTheme.typography.bodySmall,
            textAlign = TextAlign.Center
        )
    }
}

@Preview
@Composable
fun SignInPreview() {
    BarbckerTheme {
        Box(
            modifier = Modifier.fillMaxSize().background(MaterialTheme.colorScheme.background)
        ) {
            SignInScreen(
                viewState = SignInViewState(services = listOf(AuthService.GOOGLE, AuthService.APPLE)),
                onEmailChanged = {},
                onPasswordChanged = {},
                onSubmitClicked = {},
                onCreateAccountClicked= {},
                onGoogleBtnClicked = {},
                onAppleBtnClicked = {},
                onPrivacyPolicyClicked = {}
            )
        }
    }
}