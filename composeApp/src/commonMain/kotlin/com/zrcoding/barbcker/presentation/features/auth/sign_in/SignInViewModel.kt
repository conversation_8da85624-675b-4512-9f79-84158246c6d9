package com.zrcoding.barbcker.presentation.features.auth.sign_in

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import barbcker.composeapp.generated.resources.Res
import barbcker.composeapp.generated.resources.common_unexpected_error_verify_and_try_later
import barbcker.composeapp.generated.resources.sign_in_email_required
import barbcker.composeapp.generated.resources.sign_in_email_invalid
import com.zrcoding.barbcker.analytics.AnalyticsHelper
import com.zrcoding.barbcker.analytics.models.UserProperties
import com.zrcoding.barbcker.domain.models.AuthErrors
import com.zrcoding.barbcker.domain.models.AuthStatus
import com.zrcoding.barbcker.domain.models.AuthStatus.Connected.CompletionStatus.COMPLETED
import com.zrcoding.barbcker.domain.models.AuthStatus.Connected.CompletionStatus.SHOULD_COMPLETE_ACCOUNT
import com.zrcoding.barbcker.domain.models.AuthStatus.Connected.CompletionStatus.SHOULD_SETUP_BARBERS
import com.zrcoding.barbcker.domain.models.Resource
import com.zrcoding.barbcker.domain.repositories.AuthRepository
import com.zrcoding.barbcker.presentation.common.extension.renderDefault
import com.zrcoding.barbcker.presentation.common.extension.renderFailure
import com.zrcoding.barbcker.presentation.design_system.utils.UiText
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch

class SignInViewModel(
    private val authRepository: AuthRepository,
    private val analyticsHelper: AnalyticsHelper
) : ViewModel() {

    private val _viewState = MutableStateFlow(SignInViewState())
    val viewState = _viewState.asStateFlow()

    private val _oneTimeEvents = MutableSharedFlow<SignInOneTimeEvents>()
    val oneTimeEvents = _oneTimeEvents.asSharedFlow()

    fun onEmailChanged(email: String) {
        _viewState.value = viewState.value.copy(email = email.trim(), emailError = null)
    }

    fun onPasswordChanged(password: String) {
        _viewState.value = viewState.value.copy(password = password, passwordError = null)
    }

    fun onSubmit() {
        val email = viewState.value.email.trim()
        val password = viewState.value.password.trim()
        if (email.isBlank()) {
            _viewState.value = viewState.value.copy(emailError = Res.string.sign_in_email_required)
            return
        }
        
        // TODO ADD email validation
        
        if (password.isBlank()) {
            _viewState.value = viewState.value.copy(passwordError = Res.string.sign_in_email_required)
            return
        }

        authenticate {
            authRepository.authenticateWithEmailAndPassword(email = email, password = password)
        }
    }

    fun onGoogleBtnClicked() {
        authenticate { authRepository.authenticateWithGoogle() }
    }

    fun onAppleBtnClicked() {
        authenticate { authRepository.authenticateWithApple() }
    }

    private fun authenticate(auth: suspend () -> Resource<AuthStatus, AuthErrors>) {
        viewModelScope.launch {
            _viewState.update { it.copy(isLoading = true) }
            val result = auth()
            _viewState.update { it.copy(isLoading = false) }
            when (result) {
                is Resource.Success -> {
                    when (result.data) {
                        AuthStatus.Canceled -> renderDefault(UiText.FromRes(Res.string.common_unexpected_error_verify_and_try_later))
                        is AuthStatus.Connected -> {
                            analyticsHelper.setUserProperties(
                                UserProperties(
                                    uid = result.data.uid,
                                    name = result.data.name,
                                    phone = result.data.phoneNumber,
                                    email = result.data.email,
                                )
                            )
                            when (result.data.completionStatus) {
                                SHOULD_COMPLETE_ACCOUNT -> SignInOneTimeEvents.NavigateToCompleteAccount
                                SHOULD_SETUP_BARBERS -> SignInOneTimeEvents.NavigateToSetupBarbers
                                COMPLETED -> SignInOneTimeEvents.NavigateToHome
                            }.let { _oneTimeEvents.emit(it) }
                        }
                    }
                }

                is Resource.Failure -> when (val failure = result.error) {
                    is AuthErrors.Network -> renderFailure(failure.error)
                    is AuthErrors.ShouldRelogIn -> Unit
                }
            }
        }
    }
}