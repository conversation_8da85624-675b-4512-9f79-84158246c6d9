package com.zrcoding.barbcker.presentation.features.stats

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import app.cash.paging.PagingData
import app.cash.paging.map
import com.zrcoding.barbcker.domain.models.Account
import com.zrcoding.barbcker.domain.models.Currency
import com.zrcoding.barbcker.domain.repositories.AccountRepository
import com.zrcoding.barbcker.domain.repositories.StatsRepository
import com.zrcoding.barbcker.domain.utils.StatsPeriod
import com.zrcoding.barbcker.presentation.common.extension.round
import com.zrcoding.barbcker.presentation.features.common.model.BarberRevenueUiModel
import com.zrcoding.barbcker.presentation.features.common.model.toUiModel
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.flatMapLatest
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch

@OptIn(ExperimentalCoroutinesApi::class)
class StatsViewModel(
    private val statsRepository: StatsRepository,
    private val accountRepository: AccountRepository,
) : ViewModel() {

    private val periodEvents = MutableStateFlow(StatsPeriod.TODAY)

    val barbersRevenue: Flow<PagingData<BarberRevenueUiModel>> = combine(
        flow = periodEvents,
        flow2 = accountRepository.getAccount(),
        transform = { period, account ->
            Pair(period, account)
        }
    ).flatMapLatest { periodAndAccount ->
        statsRepository.getBarbersRevenueForPeriod(periodAndAccount.first)
            .map { pagingData ->
                // TODO Call logout if account is not connected
                val currency = ((periodAndAccount.second as? Account.Connected)?.currency
                    ?: Currency.DOLLAR).symbol
                pagingData.map { it.toUiModel(currencySymbol = currency) }
            }
    }

    private val _viewState = MutableStateFlow(StatsViewState())
    val viewState = _viewState.asStateFlow()

    init {
        viewModelScope.launch {
            periodEvents.flatMapLatest { period ->
                _viewState.update { it.copy(period = period) }
                combine(
                    flow = accountRepository.getAccount(),
                    flow2 = statsRepository.getTotalRevenueForPeriod(period),
                    flow3 = statsRepository.getShopOwnerTotalRevenueForPeriod(period),
                    flow4 = statsRepository.getTotalTipsForPeriod(period),
                    flow5 = statsRepository.getBarbersRevenueForPeriod(period)
                ) { account, totalRevenue, shopOwnerTotalRevenue, totalTips, barbersRevenue ->
                    // TODO Call logout if account is not connected
                    val currencySymbol =
                        ((account as? Account.Connected)?.currency ?: Currency.DOLLAR).symbol
                    _viewState.value.copy(
                        totalRevenue = "${totalRevenue.round()} $currencySymbol",
                        shopOwnerTotalRevenue = "${shopOwnerTotalRevenue.round()} $currencySymbol",
                        totalTips = "${totalTips.round()} $currencySymbol",
                    )
                }
            }.collectLatest { state -> _viewState.update { state } }
        }
    }

    fun onPeriodChanged(period: StatsPeriod) {
        viewModelScope.launch { periodEvents.emit(period) }
    }
}