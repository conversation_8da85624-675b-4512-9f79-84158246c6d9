package com.zrcoding.barbcker.presentation.navigation

import kotlinx.serialization.Serializable

@Serializable
data object Onboarding

@Serializable
data object SignIn

@Serializable
data object SignUp

@Serializable
data object CompleteAccount

@Serializable
data object SetupBarbers

@Serializable
data class UpsertBarber(val barberId: String? = null)

@Serializable
data object Home

@Serializable
data object ForYou

@Serializable
data object Barbers

@Serializable
data object Stats

@Serializable
data object Settings

@Serializable
data object CreateHaircut

@Serializable
data class HaircutList(val barberId: String? = null)

@Serializable
data class WebViewScreen(val url: String, val title: String? = null)
