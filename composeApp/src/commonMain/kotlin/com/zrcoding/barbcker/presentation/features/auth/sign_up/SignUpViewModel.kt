package com.zrcoding.barbcker.presentation.features.auth.sign_up

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import barbcker.composeapp.generated.resources.Res
import barbcker.composeapp.generated.resources.sign_in_email_already_exists
import barbcker.composeapp.generated.resources.sign_in_email_required
import barbcker.composeapp.generated.resources.sign_in_password_confirmation_mismatch
import com.zrcoding.barbcker.analytics.AnalyticsHelper
import com.zrcoding.barbcker.domain.models.Resource
import com.zrcoding.barbcker.domain.models.SignUpErrors
import com.zrcoding.barbcker.domain.repositories.AuthRepository
import com.zrcoding.barbcker.presentation.common.extension.renderFailure
import com.zrcoding.barbcker.presentation.design_system.utils.UiText
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch

class SignUpViewModel(
    private val authRepository: AuthRepository,
    private val analyticsHelper: AnalyticsHelper
) : ViewModel() {

    private val _viewState = MutableStateFlow(SignUpViewState())
    val viewState = _viewState.asStateFlow()

    private val _oneTimeEvents = MutableSharedFlow<SignUpOneTimeEvents>()
    val oneTimeEvents = _oneTimeEvents.asSharedFlow()

    fun onEmailChanged(email: String) {
        _viewState.value = viewState.value.copy(email = email.trim(), emailError = null)
    }

    fun onPasswordChanged(password: String) {
        _viewState.value = viewState.value.copy(password = password, passwordError = null)
    }

    fun onPasswordConfirmationChanged(passwordConfirmation: String) {
        _viewState.value = viewState.value.copy(
            passwordConfirmation = passwordConfirmation,
            passwordConfirmationError = null
        )
    }

    fun onSubmit() {
        val email = viewState.value.email.trim()
        val password = viewState.value.password.trim()
        val passwordConfirmation = viewState.value.passwordConfirmation.trim()
        if (email.isBlank()) {
            _viewState.value = viewState.value.copy(emailError = Res.string.sign_in_email_required)
            return
        }
        if (password.isBlank()) {
            _viewState.value =
                viewState.value.copy(passwordError = Res.string.sign_in_email_required)
            return
        }
        if (passwordConfirmation.isBlank()) {
            _viewState.value =
                viewState.value.copy(passwordConfirmationError = Res.string.sign_in_email_required)
            return
        }
        if (password != passwordConfirmation) {
            _viewState.value = viewState.value.copy(
                passwordConfirmationError = Res.string.sign_in_password_confirmation_mismatch
            )
            return
        }

        authenticate {
            authRepository.signUpWithEmailAndPassword(email = email, password = password)
        }
    }

    private fun authenticate(auth: suspend () -> Resource<Unit, SignUpErrors>) {
        viewModelScope.launch {
            _viewState.update { it.copy(isLoading = true) }
            val result = auth()
            _viewState.update { it.copy(isLoading = false) }
            when (result) {
                is Resource.Success -> {
                    _oneTimeEvents.emit(SignUpOneTimeEvents.NavigateToCompleteAccount)
                }

                is Resource.Failure -> when (val failure = result.error) {
                    is SignUpErrors.Network -> renderFailure(failure.error)
                    is SignUpErrors.EmailAlreadyExists -> renderFailure(
                        UiText.FromRes(Res.string.sign_in_email_already_exists)
                    )
                }
            }
        }
    }
}