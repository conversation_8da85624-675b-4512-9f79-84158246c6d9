package com.zrcoding.barbcker.presentation.features.stats

import androidx.compose.runtime.Stable
import barbcker.composeapp.generated.resources.Res
import barbcker.composeapp.generated.resources.stats_period_this_month
import barbcker.composeapp.generated.resources.stats_period_this_week
import barbcker.composeapp.generated.resources.stats_period_today
import com.zrcoding.barbcker.domain.utils.StatsPeriod

@Stable
data class StatsViewState(
    val period: StatsPeriod = StatsPeriod.TODAY,
    val totalRevenue: String = "0.0",
    val shopOwnerTotalRevenue: String = "0.0",
    val totalTips: String = "0.0",
)

fun StatsPeriod.stringRes() = when (this) {
    StatsPeriod.TODAY -> Res.string.stats_period_today
    StatsPeriod.THIS_WEEK -> Res.string.stats_period_this_week
    StatsPeriod.THIS_MONTH -> Res.string.stats_period_this_month
}
