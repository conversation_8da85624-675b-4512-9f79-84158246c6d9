package com.zrcoding.barbcker.domain.repositories

import app.cash.paging.PagingData
import com.zrcoding.barbcker.domain.models.BarberRevenue
import com.zrcoding.barbcker.domain.utils.StatsPeriod
import kotlinx.coroutines.flow.Flow

interface StatsRepository {

    fun getTotalRevenueForPeriod(period: StatsPeriod): Flow<Double>

    fun getShopOwnerTotalRevenueForPeriod(period: StatsPeriod): Flow<Double>

    fun getTotalTipsForPeriod(period: StatsPeriod): Flow<Double>

    fun getBarbersRevenueForPeriod(period: StatsPeriod): Flow<PagingData<BarberRevenue>>
}