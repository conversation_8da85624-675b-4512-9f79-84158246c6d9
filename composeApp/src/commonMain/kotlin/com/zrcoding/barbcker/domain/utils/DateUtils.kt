package com.zrcoding.barbcker.domain.utils

import kotlinx.datetime.DateTimeUnit
import kotlinx.datetime.LocalDate
import kotlinx.datetime.TimeZone
import kotlinx.datetime.atTime
import kotlinx.datetime.minus
import kotlinx.datetime.plus
import kotlinx.datetime.toInstant
import kotlinx.datetime.toLocalDateTime
import kotlin.time.Clock.System
import kotlin.time.ExperimentalTime

object DateUtils {

    /**
     * Get the start and end timestamps for today (00:00:00 to 23:59:59)
     */
    @OptIn(ExperimentalTime::class)
    fun getTodayRange(): Pair<Long, Long> {
        val now = System.now()
        val timeZone = TimeZone.currentSystemDefault()
        val today = now.toLocalDateTime(timeZone).date

        val startOfDay = today.atTime(0, 0, 0, 0).toInstant(timeZone)
        val endOfDay = today.atTime(23, 59, 59, 999_999_999).toInstant(timeZone)

        return Pair(startOfDay.toEpochMilliseconds(), endOfDay.toEpochMilliseconds())
    }

    /**
     * Get the start and end timestamps for this week (Monday to Sunday)
     */
    @OptIn(ExperimentalTime::class)
    fun getThisWeekRange(): Pair<Long, Long> {
        val now = System.now()
        val timeZone = TimeZone.currentSystemDefault()
        val today = now.toLocalDateTime(timeZone).date

        // Find Monday of this week
        val daysFromMonday = today.dayOfWeek.ordinal // Monday = 0, Sunday = 6
        val monday = today.minus(daysFromMonday, DateTimeUnit.DAY)
        val sunday = monday.plus(6, DateTimeUnit.DAY)

        val startOfWeek = monday.atTime(0, 0, 0, 0).toInstant(timeZone)
        val endOfWeek = sunday.atTime(23, 59, 59, 999_999_999).toInstant(timeZone)

        return Pair(startOfWeek.toEpochMilliseconds(), endOfWeek.toEpochMilliseconds())
    }

    /**
     * Get the start and end timestamps for this month (1st to last day)
     */
    @OptIn(ExperimentalTime::class)
    fun getThisMonthRange(): Pair<Long, Long> {
        val now = System.now()
        val timeZone = TimeZone.currentSystemDefault()
        val today = now.toLocalDateTime(timeZone).date

        // First day of the month
        val firstDayOfMonth = LocalDate(today.year, today.month, 1)
        // Last day of the month - get first day of next month and subtract 1 day
        val firstDayOfNextMonth = firstDayOfMonth.plus(1, DateTimeUnit.MONTH)
        val lastDayOfMonth = firstDayOfNextMonth.minus(1, DateTimeUnit.DAY)

        val startOfMonth = firstDayOfMonth.atTime(0, 0, 0, 0).toInstant(timeZone)
        val endOfMonth = lastDayOfMonth.atTime(23, 59, 59, 999_999_999).toInstant(timeZone)

        return Pair(startOfMonth.toEpochMilliseconds(), endOfMonth.toEpochMilliseconds())
    }

    /**
     * Get the start and end timestamps for a custom date range
     */
    @OptIn(ExperimentalTime::class)
    fun getCustomRange(startDate: LocalDate, endDate: LocalDate): Pair<Long, Long> {
        val timeZone = TimeZone.currentSystemDefault()

        val startOfRange = startDate.atTime(0, 0, 0, 0).toInstant(timeZone)
        val endOfRange = endDate.atTime(23, 59, 59, 999_999_999).toInstant(timeZone)

        return Pair(startOfRange.toEpochMilliseconds(), endOfRange.toEpochMilliseconds())
    }
}

/**
 * Enum representing different time periods for stats
 */
enum class StatsPeriod {
    TODAY,
    THIS_WEEK,
    THIS_MONTH
}

/**
 * Extension function to get timestamp range for a StatsPeriod
 */
fun StatsPeriod.getTimestampRange(): Pair<Long, Long> {
    return when (this) {
        StatsPeriod.TODAY -> DateUtils.getTodayRange()
        StatsPeriod.THIS_WEEK -> DateUtils.getThisWeekRange()
        StatsPeriod.THIS_MONTH -> DateUtils.getThisMonthRange()
    }
}
