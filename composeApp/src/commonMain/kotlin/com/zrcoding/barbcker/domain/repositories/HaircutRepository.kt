package com.zrcoding.barbcker.domain.repositories

import app.cash.paging.PagingData
import com.zrcoding.barbcker.domain.models.Barber
import com.zrcoding.barbcker.domain.models.HaircutWithBarber
import com.zrcoding.barbcker.domain.utils.StatsPeriod
import kotlinx.coroutines.flow.Flow

interface HaircutRepository {
    suspend fun insert(barber: <PERSON>, price: Double, tip: Double)

    fun getLatestHaircuts(period: StatsPeriod, limit: Int): Flow<List<HaircutWithBarber>>

    fun observeAll(): Flow<PagingData<HaircutWithBarber>>

    fun observeBarberHaircuts(barberId: String): Flow<PagingData<HaircutWithBarber>>
}