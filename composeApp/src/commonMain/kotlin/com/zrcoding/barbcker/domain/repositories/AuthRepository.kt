package com.zrcoding.barbcker.domain.repositories

import com.zrcoding.barbcker.domain.models.AuthErrors
import com.zrcoding.barbcker.domain.models.AuthStatus
import com.zrcoding.barbcker.domain.models.Resource
import com.zrcoding.barbcker.domain.models.SignUpErrors

interface AuthRepository {
    suspend fun authenticateWithEmailAndPassword(
        email: String,
        password: String
    ): Resource<AuthStatus, AuthErrors>

    suspend fun authenticateWithGoogle(): Resource<AuthStatus, AuthErrors>

    suspend fun authenticateWithApple(): Resource<AuthStatus, AuthErrors>

    suspend fun signUpWithEmailAndPassword(
        email: String,
        password: String
    ): Resource<Unit, SignUpErrors>
}