package com.zrcoding.barbcker.domain.repositories

import com.tweener.passage.model.Entrant
import com.zrcoding.barbcker.domain.models.Account
import com.zrcoding.barbcker.domain.models.AuthErrors
import com.zrcoding.barbcker.domain.models.Currency
import com.zrcoding.barbcker.domain.models.Resource
import kotlinx.coroutines.flow.Flow

interface AccountRepository {
    suspend fun saveAccount(entrant: Entrant, shopName: String? = null, currency: Currency? = null)

    suspend fun updateAccount(
        shopName: String,
        currency: Currency
    ): Resource<Unit, AuthErrors>

    fun getAccount(): Flow<Account>

    suspend fun logout(): Resource<Unit, AuthErrors>

    suspend fun deleteAccount(): Resource<Unit, AuthErrors>

    // SETTINGS
    suspend fun isLanguageSelected(): Boolean

    suspend fun setSelectedLanguage()
}